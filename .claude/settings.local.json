{"permissions": {"allow": ["Bash(rm:*)", "Bash(ls:*)", "Bash(node:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run build:*)", "Bash(bun run:*)", "Bash(npx tsc:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(bun add:*)", "Bash(bun remove:*)", "Bash(npm run lint)", "Bash(git init:*)", "Bash(git add:*)", "Bash(gh:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(rg:*)", "mcp__supabase__list_projects", "mcp__supabase__execute_sql", "mcp__supabase__list_tables", "Bash(bunx tsc:*)", "Bash(bun install:*)", "<PERSON><PERSON>(mv:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__docker-gateway__perplexity_research", "mcp__docker-gateway__firecrawl_search", "<PERSON><PERSON>(echo:*)"], "deny": []}}