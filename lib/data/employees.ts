import type { Employee, EmployeeDetails } from '../types'
import { db } from '../db'

export async function getEmployees(): Promise<Employee[]> {
  try {
    const employees = await db.getEmployees()
    return employees.map(emp => ({
      id: emp.id,
      fullName: emp.full_name,
      compensation: emp.rate as 'hourly' | 'monthly', // DB rate field contains the compensation type
      rate: Number(emp.compensation), // DB compensation field contains the numeric rate
      departmentId: emp.department_id || '',
      departmentName: emp.department_name,
      managerId: emp.manager_id,
      managerName: emp.manager_name,
      active: emp.active
    }))
  } catch (error) {
    console.error('Failed to fetch employees from database:', error)
    // Fallback to empty array in case of database error
    return []
  }
}

export async function saveEmployee(emp: Partial<Employee>): Promise<void> {
  try {
    if (emp.id) {
      await db.updateEmployee(emp.id, {
        fullName: emp.fullName,
        compensation: emp.rate, // DB expects numeric rate in compensation field
        rate: emp.compensation, // DB expects compensation type in rate field
        departmentId: emp.departmentId,
        managerId: emp.managerId || undefined,
        active: emp.active
      })
    } else {
      await db.createEmployee({
        fullName: emp.fullName!,
        compensation: emp.rate!, // DB expects numeric rate in compensation field
        rate: emp.compensation!, // DB expects compensation type in rate field
        departmentId: emp.departmentId!,
        managerId: emp.managerId || undefined
      })
    }
  } catch (error) {
    console.error('Failed to save employee to database:', error)
    throw new Error('Failed to save employee')
  }
}

export async function getEmployeeDetails(employeeId: string): Promise<EmployeeDetails | null> {
  try {
    const employee = await db.getEmployeeById(employeeId)
    if (!employee) return null

    return {
      id: employee.id,
      fullName: employee.full_name,
      departmentName: employee.department_name || 'N/A',
      compensation: employee.rate as 'hourly' | 'monthly', // DB rate field contains compensation type
      rate: Number(employee.compensation), // DB compensation field contains numeric rate
    }
  } catch (error) {
    console.error('Failed to fetch employee details:', error)
    return null
  }
}