"use server"

import {
  checkRateLimit,
  logUserAction,
  validateSession
} from "../auth"
import {
  handleServerActionError,
  RateLimitError
} from "./shared"

export async function searchAllEntities(query: string) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'search', 30, 60000)) { // 30 per minute
      throw new RateLimitError()
    }

    if (!query.trim()) {
      return []
    }

    // Import auth function to get current user role
    const { getCurrentUser } = await import('../auth')
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return []
    }

    // Import and use the new FTS search function
    const { searchAllEntitiesFTS } = await import('../search-fts')

    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 [SEARCH DEBUG] Using FTS search for query:', query)
      console.log('🔍 [SEARCH DEBUG] Query length:', query.length)
      console.log('👤 [SEARCH DEBUG] User role:', currentUser.role)
    }

    const searchResults = await searchAllEntitiesFTS(query, currentUser.role)

    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 [SEARCH DEBUG] FTS results:', {
        totalResults: searchResults.length,
        results: searchResults.map(r => ({ type: r.type, title: r.title, score: r.adjustedScore }))
      })
    }

    // Transform FTS results to match expected format (remove relevance scores)
    const finalResults = searchResults.map(result => ({
      id: result.id,
      title: result.title,
      subtitle: result.subtitle,
      href: result.href,
      type: result.type,
      icon: result.icon
    }))

    // Log search action (for analytics, rate limiting)
    await logUserAction('search:query', {
      query: query.trim(),
      resultCount: finalResults.length,
      userId: session.userId,
    })

    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 [SEARCH DEBUG] Final results being returned:', finalResults)
    }
    return finalResults

  } catch (error) {
    console.error('❌ [SEARCH ERROR] FTS search failed, falling back to empty results:', error)
    return []
  }
}