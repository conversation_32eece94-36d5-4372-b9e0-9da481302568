"use client"

import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { paymentStatusOptions } from "@/lib/constants/appraisal-ratings"
import type { AppraisalDetails } from "@/lib/types"

type PaymentStatusSectionProps = {
  formData: Partial<AppraisalDetails>
  onInputChange: (field: keyof AppraisalDetails, value: any) => void
  disabled: boolean
  errors: {
    paymentStatus?: { message: string }
  }
}

export function PaymentStatusSection({
  formData,
  onInputChange,
  disabled,
  errors,
}: PaymentStatusSectionProps) {
  return (
    <div className="space-y-2 border-t pt-4">
      <Label className="text-sm font-medium">Payment Status <span className="text-red-500">*</span></Label>
      <p className="text-sm text-muted-foreground">
        This status will be shown in the accounting dashboard instead of "Submitted"
      </p>
      <RadioGroup
        value={formData.paymentStatus || ""}
        onValueChange={(value) => onInputChange("paymentStatus", value as "ready-to-pay" | "contact-manager" | null)}
        className="flex flex-row gap-6"
        disabled={disabled}
      >
        {paymentStatusOptions.map((option) => (
          <div key={option.value} className="flex items-center space-x-2">
            <RadioGroupItem value={option.value} id={option.value} />
            <Label htmlFor={option.value} className="text-sm">{option.label}</Label>
          </div>
        ))}
      </RadioGroup>
      {errors.paymentStatus && (
        <p
          className="text-sm text-red-500"
          role="alert"
          aria-live="polite"
        >
          {errors.paymentStatus.message}
        </p>
      )}
      <p className="text-xs text-muted-foreground">
        This status will be shown in the accounting dashboard instead of "Submitted"
      </p>
    </div>
  )
}