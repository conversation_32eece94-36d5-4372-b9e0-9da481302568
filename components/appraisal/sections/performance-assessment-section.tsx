"use client"

import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { appraisalQuestions } from "@/lib/constants/appraisal-questions"
import type { AppraisalDetails } from "@/lib/types"

type PerformanceAssessmentSectionProps = {
  formData: Partial<AppraisalDetails>
  onInputChange: (field: keyof AppraisalDetails, value: any) => void
  disabled: boolean
  errors: {
    keyContributions?: { message: string }
    extraInitiatives?: { message: string }
    performanceLacking?: { message: string }
    daysOffTaken?: { message: string }
  }
}

export function PerformanceAssessmentSection({
  formData,
  onInputChange,
  disabled,
  errors,
}: PerformanceAssessmentSectionProps) {
  return (
    <div className="space-y-8 border-t pt-8">
      <h3 className="text-lg font-semibold">Detailed Performance Assessment</h3>

      <div className="space-y-2">
        <Label htmlFor="keyContributions">{appraisalQuestions.keyContributions} <span className="text-red-500">*</span></Label>
        <Textarea
          id="keyContributions"
          value={formData.keyContributions}
          onChange={(e) => onInputChange("keyContributions", e.target.value)}
          placeholder="Describe the employee's most valuable outputs and contributions this period..."
          rows={4}
          disabled={disabled}
        />
        {errors.keyContributions && (
          <p className="text-sm text-red-500">{errors.keyContributions.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="extraInitiatives">{appraisalQuestions.extraInitiatives} <span className="text-red-500">*</span></Label>
        <Textarea
          id="extraInitiatives"
          value={formData.extraInitiatives}
          onChange={(e) => onInputChange("extraInitiatives", e.target.value)}
          placeholder="Describe any initiatives beyond core responsibilities, innovation, ownership, cross-team help..."
          rows={4}
          disabled={disabled}
        />
        {errors.extraInitiatives && (
          <p className="text-sm text-red-500">{errors.extraInitiatives.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="performanceLacking">{appraisalQuestions.performanceLacking} <span className="text-red-500">*</span></Label>
        <Textarea
          id="performanceLacking"
          value={formData.performanceLacking}
          onChange={(e) => onInputChange("performanceLacking", e.target.value)}
          placeholder="Describe any missed deadlines, quality issues, lack of clarity or follow-through..."
          rows={4}
          disabled={disabled}
        />
        {errors.performanceLacking && (
          <p className="text-sm text-red-500">{errors.performanceLacking.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="daysOffTaken">{appraisalQuestions.daysOffTaken} <span className="text-red-500">*</span></Label>
        <Input
          id="daysOffTaken"
          type="number"
          min="0"
          value={formData.daysOffTaken ?? ""}
          onChange={(e) => onInputChange("daysOffTaken", e.target.value ? parseInt(e.target.value) : null)}
          placeholder="0"
          disabled={disabled}
        />
        {errors.daysOffTaken && (
          <p className="text-sm text-red-500">{errors.daysOffTaken.message}</p>
        )}
      </div>
    </div>
  )
}