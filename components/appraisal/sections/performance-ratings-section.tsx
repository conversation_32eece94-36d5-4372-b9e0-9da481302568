"use client"

import { RatingField } from "@/components/appraisal/rating-field"
import { appraisalQuestions } from "@/lib/constants/appraisal-questions"
import type { AppraisalDetails } from "@/lib/types"

type PerformanceRatingsSectionProps = {
  formData: Partial<AppraisalDetails>
  onInputChange: (field: keyof AppraisalDetails, value: any) => void
  disabled: boolean
  errors: {
    impactRating?: { message: string }
    qualityRating?: { message: string }
    collaborationRating?: { message: string }
    skillGrowthRating?: { message: string }
  }
}

export function PerformanceRatingsSection({
  formData,
  onInputChange,
  disabled,
  errors,
}: PerformanceRatingsSectionProps) {
  return (
    <div className="space-y-8 border-t pt-8">
      <h3 className="text-lg font-semibold">Performance Ratings</h3>

      <RatingField
        question={appraisalQuestions.impactRating}
        value={formData.impactRating}
        onRatingChange={(value) => onInputChange("impactRating", value)}
        comment={formData.impactComment}
        onCommentChange={(value) => onInputChange("impactComment", value)}
        commentPlaceholder="Optional comment for impact rating..."
        disabled={disabled}
        required
        error={errors.impactRating?.message}
      />

      <RatingField
        question={appraisalQuestions.qualityRating}
        value={formData.qualityRating}
        onRatingChange={(value) => onInputChange("qualityRating", value)}
        comment={formData.qualityComment}
        onCommentChange={(value) => onInputChange("qualityComment", value)}
        commentPlaceholder="Optional comment for quality rating..."
        disabled={disabled}
        required
        error={errors.qualityRating?.message}
      />

      <RatingField
        question={appraisalQuestions.collaborationRating}
        value={formData.collaborationRating}
        onRatingChange={(value) => onInputChange("collaborationRating", value)}
        comment={formData.collaborationComment}
        onCommentChange={(value) => onInputChange("collaborationComment", value)}
        commentPlaceholder="Optional comment for collaboration rating..."
        disabled={disabled}
        required
        error={errors.collaborationRating?.message}
      />

      <RatingField
        question={appraisalQuestions.skillGrowthRating}
        value={formData.skillGrowthRating}
        onRatingChange={(value) => onInputChange("skillGrowthRating", value)}
        comment={formData.skillGrowthComment}
        onCommentChange={(value) => onInputChange("skillGrowthComment", value)}
        commentPlaceholder="Optional comment for skill growth rating..."
        disabled={disabled}
        required
        error={errors.skillGrowthRating?.message}
      />
    </div>
  )
}