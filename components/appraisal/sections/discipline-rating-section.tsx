"use client"

import { RatingField } from "@/components/appraisal/rating-field"
import { appraisalQuestions } from "@/lib/constants/appraisal-questions"
import type { AppraisalDetails } from "@/lib/types"

type DisciplineRatingSectionProps = {
  formData: Partial<AppraisalDetails>
  onInputChange: (field: keyof AppraisalDetails, value: any) => void
  disabled: boolean
  errors: {
    disciplineRating?: { message: string }
    disciplineComment?: { message: string }
  }
}

export function DisciplineRatingSection({
  formData,
  onInputChange,
  disabled,
  errors,
}: DisciplineRatingSectionProps) {
  return (
    <RatingField
      question={appraisalQuestions.disciplineRating}
      value={formData.disciplineRating}
      onRatingChange={(value) => onInputChange("disciplineRating", value)}
      comment={formData.disciplineComment}
      onCommentChange={(value) => onInputChange("disciplineComment", value)}
      commentLabel="Comment:"
      commentPlaceholder="Any concerns with presence or conduct..."
      disabled={disabled}
      required
      error={errors.disciplineRating?.message}
      commentRequired
      commentError={errors.disciplineComment?.message}
    />
  )
}