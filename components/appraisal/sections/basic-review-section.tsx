"use client"

import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { appraisalQuestions } from "@/lib/constants/appraisal-questions"
import { overallPerformanceOptions } from "@/lib/constants/appraisal-ratings"
import type { AppraisalDetails } from "@/lib/types"

type BasicReviewSectionProps = {
  formData: Partial<AppraisalDetails>
  onInputChange: (field: keyof AppraisalDetails, value: any) => void
  disabled: boolean
  errors: {
    q1?: { message: string }
    q3?: { message: string }
    q4?: { message: string }
    q5?: { message: string }
  }
}

export function BasicReviewSection({
  formData,
  onInputChange,
  disabled,
  errors,
}: BasicReviewSectionProps) {
  return (
    <div className="space-y-8">
      <fieldset className="space-y-2">
        <legend className="text-sm font-medium">
          {appraisalQuestions.q1} <span className="text-red-500" aria-label="required">*</span>
        </legend>
        <RadioGroup
          value={formData.q1 ?? ""}
          onValueChange={(value) => onInputChange("q1", value as AppraisalDetails["q1"])}
          className="flex flex-col space-y-2 pt-2 sm:flex-row sm:space-y-0 sm:space-x-4"
          disabled={disabled}
          aria-required="true"
          aria-describedby={errors.q1 ? "q1-error" : undefined}
        >
          {overallPerformanceOptions.map((option) => (
            <div key={option.value} className="flex items-center space-x-2">
              <RadioGroupItem value={option.value} id={`q1-${option.value}`} />
              <Label htmlFor={`q1-${option.value}`}>{option.label}</Label>
            </div>
          ))}
        </RadioGroup>
        {errors.q1 && (
          <p
            id="q1-error"
            className="text-sm text-red-500"
            role="alert"
            aria-live="polite"
          >
            {errors.q1.message}
          </p>
        )}
      </fieldset>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="q2"
          checked={formData.q2}
          onCheckedChange={(checked) => onInputChange("q2", !!checked)}
          disabled={disabled}
        />
        <Label
          htmlFor="q2"
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          {appraisalQuestions.q2}
        </Label>
      </div>

      <div className="space-y-2">
        <Label htmlFor="q3">{appraisalQuestions.q3} <span className="text-red-500">*</span></Label>
        <Input
          id="q3"
          value={formData.q3}
          onChange={(e) => onInputChange("q3", e.target.value)}
          placeholder="e.g., Project Phoenix, Q3 Marketing Campaign"
          disabled={disabled}
        />
        {errors.q3 && (
          <p className="text-sm text-red-500">{errors.q3.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="q4">{appraisalQuestions.q4} <span className="text-red-500">*</span></Label>
        <Textarea
          id="q4"
          value={formData.q4}
          onChange={(e) => onInputChange("q4", e.target.value)}
          placeholder="Summarize key accomplishments and any challenges faced..."
          rows={5}
          disabled={disabled}
        />
        {errors.q4 && (
          <p className="text-sm text-red-500">{errors.q4.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="q5">{appraisalQuestions.q5} <span className="text-red-500">*</span></Label>
        <Input
          id="q5"
          value={formData.q5}
          onChange={(e) => onInputChange("q5", e.target.value)}
          placeholder="e.g., Lead design for new feature, improve API documentation"
          disabled={disabled}
        />
        {errors.q5 && (
          <p className="text-sm text-red-500">{errors.q5.message}</p>
        )}
      </div>
    </div>
  )
}