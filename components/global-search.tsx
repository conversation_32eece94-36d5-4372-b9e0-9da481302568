"use client"

import React from "react"
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command"
import { Badge } from "@/components/ui/badge"
import { useGlobalSearch } from "@/hooks/use-global-search"
import {
  User,
  Building2,
  UserCheck,
  Calendar,
  Navigation,
  CalendarDays,
  Search,
  Clock,
  Loader2,
} from "lucide-react"

const iconMap = {
  user: User,
  building2: Building2,
  "user-check": UserCheck,
  calendar: Calendar,
  navigation: Navigation,
  "calendar-days": CalendarDays,
}

const typeLabels = {
  employee: "Employee",
  department: "Department",
  manager: "Manager",
  period: "Appraisal Period",
  navigation: "Page",
  pto: "PTO Request",
}

const typeColors = {
  employee: "bg-blue-100 text-blue-800",
  department: "bg-green-100 text-green-800",
  manager: "bg-purple-100 text-purple-800",
  period: "bg-orange-100 text-orange-800",
  navigation: "bg-gray-100 text-gray-800",
  pto: "bg-pink-100 text-pink-800",
}

export function GlobalSearch() {
  const {
    isOpen,
    setIsOpen,
    query,
    setQuery,
    results,
    loading,
    selectResult,
    showingRecent,
  } = useGlobalSearch()

  // Group results by type with error handling
  const groupedResults = React.useMemo(() => {
    try {
      if (!Array.isArray(results)) {
        console.warn('🔍 [SEARCH UI] Results is not an array:', results)
        return {}
      }

      return results.reduce((acc, result) => {
        if (!result || typeof result !== 'object') {
          console.warn('🔍 [SEARCH UI] Invalid result object:', result)
          return acc
        }

        if (!result.type) {
          console.warn('🔍 [SEARCH UI] Result missing type:', result)
          return acc
        }

        if (!acc[result.type]) {
          acc[result.type] = []
        }
        acc[result.type].push(result)
        return acc
      }, {} as Record<string, typeof results>)
    } catch (error) {
      console.error('🔍 [SEARCH UI] Error grouping results:', error)
      return {}
    }
  }, [results])

  const handleSelect = (result: any) => {
    try {
      if (!result) {
        console.warn('🔍 [SEARCH UI] Attempted to select null/undefined result')
        return
      }
      selectResult(result)
    } catch (error) {
      console.error('🔍 [SEARCH UI] Error selecting result:', error)
    }
  }

  return (
    <CommandDialog open={isOpen} onOpenChange={setIsOpen}>
      <CommandInput
        placeholder="Search employees, departments, pages..."
        value={query}
        onValueChange={setQuery}
        className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
      />
      {loading && (
        <div className="flex items-center justify-center px-3 py-2">
          <Loader2 className="h-4 w-4 animate-spin opacity-50" />
        </div>
      )}

      <CommandList className="max-h-[400px] overflow-y-auto">
        {results.length === 0 && !loading && (
          <CommandEmpty>
            {query.trim() ? "No results found." : "Start typing to search..."}
          </CommandEmpty>
        )}

        {showingRecent && results.length > 0 && (
          <CommandGroup heading="Recent">
            {results.map((result) => {
              const IconComponent = iconMap[result.icon as keyof typeof iconMap] || User
              return (
                <CommandItem
                  key={result.id}
                  value={result.id}
                  onSelect={() => handleSelect(result)}
                  className="flex items-center gap-3 px-3 py-2"
                >
                  <div className="flex h-8 w-8 items-center justify-center rounded-md bg-muted">
                    <IconComponent className="h-4 w-4" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="font-medium truncate">{result.title}</span>
                      <Badge
                        variant="secondary"
                        className={`text-xs ${typeColors[result.type]}`}
                      >
                        {typeLabels[result.type]}
                      </Badge>
                    </div>
                    {result.subtitle && (
                      <p className="text-sm text-muted-foreground truncate">
                        {result.subtitle}
                      </p>
                    )}
                  </div>
                  <Clock className="h-3 w-3 text-muted-foreground" />
                </CommandItem>
              )
            })}
          </CommandGroup>
        )}

        {!showingRecent && (
          <>
            {Object.entries(groupedResults).map(([type, typeResults], index) => (
              <div key={type}>
                {index > 0 && <CommandSeparator />}
                <CommandGroup heading={typeLabels[type as keyof typeof typeLabels] || type}>
                  {typeResults.map((result) => {
                    if (!result || !result.id) {
                      console.warn('🔍 [SEARCH UI] Skipping invalid result:', result)
                      return null
                    }

                    const IconComponent = iconMap[result.icon as keyof typeof iconMap] || User
                    return (
                      <CommandItem
                        key={result.id}
                        value={result.id}
                        onSelect={() => handleSelect(result)}
                        className="flex items-center gap-3 px-3 py-2"
                      >
                        <div className="flex h-8 w-8 items-center justify-center rounded-md bg-muted">
                          <IconComponent className="h-4 w-4" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className="font-medium truncate">{result.title || 'Untitled'}</span>
                            <Badge
                              variant="secondary"
                              className={`text-xs ${typeColors[result.type]}`}
                            >
                              {typeLabels[result.type]}
                            </Badge>
                          </div>
                          {result.subtitle && (
                            <p className="text-sm text-muted-foreground truncate">
                              {result.subtitle}
                            </p>
                          )}
                        </div>
                      </CommandItem>
                    )
                  })}
                </CommandGroup>
              </div>
            ))}
          </>
        )}

        {!showingRecent && results.length > 0 && (
          <>
            <CommandSeparator />
            <div className="px-3 py-2 text-xs text-muted-foreground">
              <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                <span className="text-xs">⏎</span>
              </kbd>{" "}
              to select •{" "}
              <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                <span className="text-xs">↑↓</span>
              </kbd>{" "}
              to navigate •{" "}
              <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                <span className="text-xs">ESC</span>
              </kbd>{" "}
              to close
            </div>
          </>
        )}
      </CommandList>
    </CommandDialog>
  )
}