-- Add new appraisal questions to appy_appraisals table
-- This migration adds the 10 new questions requested by the user

-- Add new columns for the expanded appraisal form
ALTER TABLE appy_appraisals 
ADD COLUMN IF NOT EXISTS key_contributions TEXT,
ADD COLUMN IF NOT EXISTS extra_initiatives TEXT,
ADD COLUMN IF NOT EXISTS performance_lacking TEXT,
ADD COLUMN IF NOT EXISTS discipline_rating INTEGER CHECK (discipline_rating >= 1 AND discipline_rating <= 5),
ADD COLUMN IF NOT EXISTS discipline_comment TEXT,
ADD COLUMN IF NOT EXISTS days_off_taken INTEGER CHECK (days_off_taken >= 0),
ADD COLUMN IF NOT EXISTS impact_rating INTEGER CHECK (impact_rating >= 1 AND impact_rating <= 5),
ADD COLUMN IF NOT EXISTS impact_comment TEXT,
ADD COLUMN IF NOT EXISTS quality_rating INTEGER CHECK (quality_rating >= 1 AND quality_rating <= 5),
ADD COLUMN IF NOT EXISTS quality_comment TEXT,
ADD COLUMN IF NOT EXISTS collaboration_rating INTEGER CHECK (collaboration_rating >= 1 AND collaboration_rating <= 5),
ADD COLUMN IF NOT EXISTS collaboration_comment TEXT,
ADD COLUMN IF NOT EXISTS skill_growth_rating INTEGER CHECK (skill_growth_rating >= 1 AND skill_growth_rating <= 5),
ADD COLUMN IF NOT EXISTS skill_growth_comment TEXT,
ADD COLUMN IF NOT EXISTS readiness_promotion TEXT CHECK (readiness_promotion IN ('strong-yes', 'yes-with-reservations', 'no-not-yet')),
ADD COLUMN IF NOT EXISTS readiness_comment TEXT,
ADD COLUMN IF NOT EXISTS compensation_recommendation TEXT;

-- Add comments for documentation
COMMENT ON COLUMN appy_appraisals.key_contributions IS 'What were the employee''s most valuable outputs this period?';
COMMENT ON COLUMN appy_appraisals.extra_initiatives IS 'Did they go beyond their core responsibilities? Innovation, ownership, help across teams, etc.';
COMMENT ON COLUMN appy_appraisals.performance_lacking IS 'Missed deadlines, quality issues, lack of clarity or follow-through.';
COMMENT ON COLUMN appy_appraisals.discipline_rating IS 'Were they punctual, responsive, and consistent? Rating 1-5';
COMMENT ON COLUMN appy_appraisals.discipline_comment IS 'Any concerns with presence or conduct?';
COMMENT ON COLUMN appy_appraisals.days_off_taken IS 'Total number of days off this cycle (excluding public holidays)';
COMMENT ON COLUMN appy_appraisals.impact_rating IS 'Impact on Goals & Deliverables rating 1-5';
COMMENT ON COLUMN appy_appraisals.impact_comment IS 'Optional comment for impact rating';
COMMENT ON COLUMN appy_appraisals.quality_rating IS 'Quality of Work rating 1-5';
COMMENT ON COLUMN appy_appraisals.quality_comment IS 'Optional comment for quality rating';
COMMENT ON COLUMN appy_appraisals.collaboration_rating IS 'Collaboration & Communication rating 1-5';
COMMENT ON COLUMN appy_appraisals.collaboration_comment IS 'Optional comment for collaboration rating';
COMMENT ON COLUMN appy_appraisals.skill_growth_rating IS 'Skill Growth & Learning rating 1-5';
COMMENT ON COLUMN appy_appraisals.skill_growth_comment IS 'Optional comment for skill growth rating';
COMMENT ON COLUMN appy_appraisals.readiness_promotion IS 'Readiness for Raise / Promotion / More Responsibility';
COMMENT ON COLUMN appy_appraisals.readiness_comment IS 'Comment for readiness assessment';
COMMENT ON COLUMN appy_appraisals.compensation_recommendation IS 'Should this employee receive full salary adjustment / bonus / raise, or should it be adjusted based on performance or attendance?';

-- Create indexes for performance on rating columns (if needed for reporting)
CREATE INDEX IF NOT EXISTS idx_appraisals_discipline_rating ON appy_appraisals(discipline_rating);
CREATE INDEX IF NOT EXISTS idx_appraisals_impact_rating ON appy_appraisals(impact_rating);
CREATE INDEX IF NOT EXISTS idx_appraisals_quality_rating ON appy_appraisals(quality_rating);
CREATE INDEX IF NOT EXISTS idx_appraisals_collaboration_rating ON appy_appraisals(collaboration_rating);
CREATE INDEX IF NOT EXISTS idx_appraisals_skill_growth_rating ON appy_appraisals(skill_growth_rating);
CREATE INDEX IF NOT EXISTS idx_appraisals_readiness_promotion ON appy_appraisals(readiness_promotion);
