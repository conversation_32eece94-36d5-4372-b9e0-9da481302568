#!/usr/bin/env tsx

/**
 * Setup script to initialize Full-Text Search indexes for the Employee Appraisal System
 * 
 * This script creates GIN indexes for FTS on all searchable text fields.
 * Run this once to enable high-performance search capabilities.
 */

import { initializeFTSIndexes } from '../lib/search-fts'

async function main() {
  console.log('🚀 Setting up Full-Text Search indexes...')
  
  try {
    await initializeFTSIndexes()
    console.log('✅ FTS indexes setup completed successfully!')
    console.log('')
    console.log('🔍 Your search system is now ready to use Postgres FTS for:')
    console.log('  • Employee names with ts_rank scoring')
    console.log('  • Department names with relevance ranking')
    console.log('  • Manager names with fuzzy matching')
    console.log('  • Appraisal period names')
    console.log('  • PTO request types')
    console.log('')
    console.log('💡 The search will automatically fall back to ILIKE queries if FTS fails.')
    
    process.exit(0)
  } catch (error) {
    console.error('❌ Failed to setup FTS indexes:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}